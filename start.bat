@echo off
title Alive AI
color 0B

echo Starting Alive AI...

REM Check if alive-cli exists
if not exist "alive-cli" (
    echo Error: alive-cli directory not found.
    echo Please make sure you're running this from the project root directory.
    pause
    exit /b 1
)

REM Check if built
if not exist "alive-cli\dist\cli.js" (
    echo Building Alive AI...
    cd alive-cli
    call npm run build
    if %ERRORLEVEL% NEQ 0 (
        echo Build failed!
        pause
        exit /b 1
    )
    cd ..
)

REM Run Alive AI
cd alive-cli
node bin/alive.js %*

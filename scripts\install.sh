#!/bin/bash
set -e

# <PERSON>ript to install dependencies for Alive AI
echo "Installing dependencies for Alive AI..."

# Check for Node.js installation
echo "Checking for Node.js installation..."
if ! command -v node &> /dev/null; then
    echo "Node.js not found. Please install Node.js from https://nodejs.org/"
    echo "Alternatively, you can use nvm to install Node.js:"
    echo "curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash"
    echo "nvm install --lts"
    echo "After installing Node.js, run this script again."
    exit 1
else
    echo "Node.js is already installed: $(node --version)"
fi

# Install npm dependencies for alive-cli
echo "Installing npm dependencies for alive-cli..."
cd "$(dirname "$0")/../alive-cli"
npm ci

echo "All dependencies are installed!"
echo "You can now build the project using scripts/build.sh"
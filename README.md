# Alive AI

Alive AI is a powerful AI assistant that runs locally on your machine. It provides a Node.js CLI interface to deliver a seamless AI experience.

## Features

- **Terminal UI**: Rich interactive terminal user interface
- **File Operations**: Safely read, write, and manipulate files
- **Shell Integration**: Run shell commands directly from the AI
- **Web Access**: Fetch data from the web, perform searches, and interact with APIs (when enabled)
- **Sandbox Security**: Configurable security policies to keep your system safe
- **Cross-Platform**: Works on Windows, macOS, and Linux

## Installation

### Prerequisites

- Node.js (v22 or higher)

### Installation

```bash
# Install dependencies
cd alive-cli
npm install

# Build the project
npm run build
```

## Usage

### Starting Alive AI

```bash
cd alive-cli
node bin/alive.js
```

Or if you have the package installed globally:

```bash
alive
```

### Command-Line Options

```bash
alive --help
```

For detailed usage information, run the help command above.

## Web Tool

Alive AI includes a web tool that allows agents to fetch data from the internet, interact with APIs, and perform web searches.

### Setup

To set up the web tool dependencies, run:

```bash
# From the alive-cli directory
npm run setup-web-tool
```

### Usage

To enable the web tool, use the `--enable-web-tool` flag:

```bash
alive --enable-web-tool --full-auto "Fetch the latest news about AI"
```

### Configuration

You can manage your web tool API keys in several ways:

1. **Web Key Management Utility**:
   ```bash
   npm run manage-web-keys
   ```
   This interactive utility allows you to view, update, and clear API keys for web search providers.

2. **Command Line Arguments**:
   ```bash
   # Google Search
   alive --enable-web-tool --google-api-key "YOUR_KEY" --google-cx "YOUR_CX" --search-provider "google" "Search for..."

   # Bing Search
   alive --enable-web-tool --bing-api-key "YOUR_KEY" --search-provider "bing" "Search for..."

   # DuckDuckGo (no API key required)
   alive --enable-web-tool --search-provider "duckduckgo" "Search for..."
   ```

3. **Environment Variables**:
   - `GOOGLE_API_KEY`: Google API key
   - `GOOGLE_SEARCH_CX`: Google Custom Search Engine ID
   - `BING_API_KEY`: Bing API key
   - `ALIVE_DEFAULT_SEARCH_PROVIDER`: Default search provider

For more details, see the [web tool documentation](docs/web-tool.md).

## Architecture

Alive AI is built as a Node.js CLI application that:
- Manages AI model communication
- Handles file system operations
- Executes shell commands with sandboxing
- Enforces security policies
- Provides a rich terminal UI
- Manages configuration and user preferences

## Security

Alive AI takes security seriously. By default, it operates with restricted permissions:

- Read-only access to most of the file system
- Write access only to the current directory
- No network access by default (can be enabled with flags)

You can configure the security policy to match your needs with the `--sandbox` flag.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

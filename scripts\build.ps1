# PowerShell script to build alive-cli component
Write-Host "Building Alive AI..." -ForegroundColor Cyan

# Build the CLI component
Write-Host "Building alive-cli..." -ForegroundColor Green
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path "$scriptPath\..\alive-cli"
npm ci
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to install Node.js dependencies" -ForegroundColor Red
    exit $LASTEXITCODE
}

npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to build alive-cli" -ForegroundColor Red
    exit $LASTEXITCODE
}

Write-Host "Build completed successfully!" -ForegroundColor Cyan
# PowerShell script to install dependencies for Alive AI
Write-Host "Installing dependencies for Alive AI..." -ForegroundColor Cyan

# Get the script path
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path

# Check for Node.js installation
Write-Host "Checking for Node.js installation..." -ForegroundColor Green
$nodeVersion = node --version
if ($LASTEXITCODE -ne 0) {
    Write-Host "Node.js not found. Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    Write-Host "After installing Node.js, run this script again." -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "Node.js is already installed: $nodeVersion" -ForegroundColor Green
}

# Install npm dependencies for alive-cli
Write-Host "Installing npm dependencies for alive-cli..." -ForegroundColor Green
Set-Location -Path "$scriptPath\..\alive-cli"
npm ci
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to install npm dependencies" -ForegroundColor Red
    exit $LASTEXITCODE
}

Write-Host "All dependencies are installed!" -ForegroundColor Cyan
Write-Host "You can now build the project using scripts\build.ps1" -ForegroundColor <PERSON>an
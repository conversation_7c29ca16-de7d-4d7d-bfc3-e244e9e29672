{"name": "alive-ai-monorepo", "private": true, "description": "Alive AI - A Node.js-based AI coding assistant.", "scripts": {"release": "pnpm --filter @alive-ai/cli run release", "format": "prettier --check *.json *.md .github/workflows/*.yml", "format:fix": "prettier --write *.json *.md .github/workflows/*.yml", "build": "pnpm --filter @alive-ai/cli run build", "test": "pnpm --filter @alive-ai/cli run test", "lint": "pnpm --filter @alive-ai/cli run lint", "lint:fix": "pnpm --filter @alive-ai/cli run lint:fix", "typecheck": "pnpm --filter @alive-ai/cli run typecheck", "changelog": "git-cliff --config cliff.toml --output CHANGELOG.ignore.md $LAST_RELEASE_TAG..HEAD", "prepare": "husky", "husky:add": "husky add"}, "devDependencies": {"@types/cheerio": "^0.22.35", "@types/node": "^22.15.19", "git-cliff": "^2.8.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "^3.5.3"}, "resolutions": {"braces": "^3.0.3", "micromatch": "^4.0.8", "semver": "^7.7.1"}, "overrides": {"punycode": "^2.3.1"}, "pnpm": {"patchedDependencies": {"marked-terminal@7.3.0": "patches/<EMAIL>"}}, "engines": {"node": ">=22", "pnpm": ">=9.0.0"}, "lint-staged": {"*.json": "prettier --write", "*.md": "prettier --write", ".github/workflows/*.yml": "prettier --write", "**/*.{js,ts,tsx}": ["prettier --write", "pnpm --filter @alive-ai/cli run lint", "cd alive-cli && pnpm run typecheck"]}, "packageManager": "pnpm@10.8.1", "dependencies": {"@inkjs/ui": "^2.0.0", "axios": "^1.9.0", "chalk": "^5.2.0", "fs-extra": "^11.3.0", "ink": "^5.2.0", "jsdom": "^26.1.0", "marked-terminal": "^7.3.0", "openai": "^4.95.1", "prompts": "^2.4.2", "react": "^18.2.0", "supports-hyperlinks": "^4.1.0"}}
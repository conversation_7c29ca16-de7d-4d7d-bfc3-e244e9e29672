@echo off
setlocal enabledelayedexpansion
title Alive AI Launcher
color 0B

REM Set the base directory
set "BASE_DIR=%~dp0"
cd /d "%BASE_DIR%"

REM Check for API keys in Windows and load them
set PROVIDER_FLAG=

REM Try to read API keys from auth.json
set AUTH_FILE=%USERPROFILE%\.alive-ai\auth.json
if exist "%AUTH_FILE%" (
    echo Loading API keys from auth.json...
    for /f "tokens=*" %%a in ('powershell -Command "try { (Get-Content -Raw '%AUTH_FILE%' | ConvertFrom-Json).providerKeys.deepseek } catch { '' }"') do (
        set DEEPSEEK_API_KEY=%%a
    )
)

REM If we have a DeepSeek API key, use DeepSeek provider by default
if defined DEEPSEEK_API_KEY (
    echo Using DeepSeek provider...
    set PROVIDER_FLAG=--provider deepseek
) else (
    echo No DeepSeek API key found, will attempt to use default provider...
)

REM Check if alive-cli is available
if exist "alive-cli" (
    REM Check if the Node.js version is built
    if not exist "alive-cli\dist\cli.js" (
        echo Building Node.js version...
        cd alive-cli
        call npm run build
        cd ..
    )

    echo Running Alive AI...
    cd alive-cli
    node bin/alive.js %PROVIDER_FLAG% %*
) else (
    echo Error: alive-cli directory not found.
    echo Please make sure you're running this from the correct directory.
    pause
    exit /b 1
)
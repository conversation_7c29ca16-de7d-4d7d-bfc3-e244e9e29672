# PowerShell script to run Alive AI
Write-Host "Starting Alive AI..." -ForegroundColor Cyan

# Get the script path
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path

# Check if build is needed
if (-not (Test-Path "$scriptPath\..\alive-cli\dist\cli.js")) {
    Write-Host "Build required. Running build script first..." -ForegroundColor Yellow
    & "$scriptPath\build.ps1"
    if ($LASTEXITCODE -ne 0) {
        exit $LASTEXITCODE
    }
}

# Run the CLI
Write-Host "Launching Alive AI..." -ForegroundColor Green
Set-Location -Path "$scriptPath\..\alive-cli"
node bin/alive.js $args
{
  description = "Development Nix flake for Alive AI CLI";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs = { nixpkgs, flake-utils, ... }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pkgs = import nixpkgs {
          inherit system;
        };
        monorepo-deps = with pkgs; [
          # for precommit hook
          pnpm
          husky
        ];
        alive-cli = import ./alive-cli {
          inherit pkgs monorepo-deps;
        };
      in
      rec {
        packages = {
          alive-cli = alive-cli.package;
        };

        devShells = {
          alive-cli = alive-cli.devShell;
        };

        apps = {
          alive-cli = alive-cli.app;
        };

        defaultPackage = packages.alive-cli;
        defaultApp = apps.alive-cli;
        defaultDevShell = devShells.alive-cli;
      }
    );
}
